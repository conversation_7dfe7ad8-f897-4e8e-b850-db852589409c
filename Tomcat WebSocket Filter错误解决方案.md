# Tomcat WebSocket Filter错误解决方案

## 🎯 **错误分析**

### 错误信息
```
Failed to destroy the filter named [Tomcat WebSocket (JSR356) Filter] of type [org.apache.tomcat.websocket.server.WsFilter]
java.lang.AbstractMethodError
```

### 根本原因
这是一个典型的**JAR包版本冲突**问题，特别是在Spring Boot 2.3.12 + Dubbo 3.x环境中。

## 🔍 **问题诊断**

### 当前环境配置
- **Spring Boot版本**: 2.3.12.RELEASE
- **Java版本**: 1.8
- **Dubbo版本**: 3.x (从Dubbo 2.x升级而来)
- **Tomcat**: Spring Boot内嵌Tomcat

### 冲突分析
1. **WebSocket API版本冲突**：
   - Spring Boot 2.3.12内嵌的Tomcat版本
   - 与Dubbo 3.x依赖的WebSocket API版本不匹配

2. **Servlet API版本问题**：
   - 不同版本的Servlet API导致方法签名不匹配
   - `AbstractMethodError`表示接口方法在运行时找不到实现

3. **类加载器冲突**：
   - 多个版本的同一个类被加载
   - 运行时调用了错误版本的方法

## 🔧 **解决方案**

### 方案1: 排除冲突的WebSocket依赖（推荐）

在`high-batch-center-remote/pom.xml`中添加排除配置：

```xml
<dependency>
    <groupId>com.howbuy.tms</groupId>
    <artifactId>high-batch-center-service</artifactId>
    <exclusions>
        <!-- 现有的排除配置... -->
        
        <!-- 新增：排除WebSocket相关冲突 -->
        <exclusion>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-websocket</artifactId>
        </exclusion>
        <exclusion>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
        </exclusion>
        <exclusion>
            <groupId>javax.websocket</groupId>
            <artifactId>javax.websocket-api</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

### 方案2: 禁用WebSocket功能

在`application.yml`或`application.properties`中添加：

```yaml
# application.yml
server:
  servlet:
    context-path: /
  tomcat:
    websocket:
      enabled: false

# 或者在application.properties中
server.servlet.context-path=/
server.tomcat.websocket.enabled=false
```

### 方案3: 升级Spring Boot版本

考虑升级到与Dubbo 3.x更兼容的Spring Boot版本：

```xml
<parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.7.18</version> <!-- 或更新的稳定版本 -->
    <relativePath />
</parent>
```

### 方案4: 在启动类中禁用WebSocket

在`HighBatchCenterApplication.java`中添加：

```java
@SpringBootApplication(
    scanBasePackages = {"com.howbuy.tms.high.batch", "com.howbuy.tms.common.simu.outerservice"}, 
    exclude = {
        DataSourceAutoConfiguration.class,
        // 新增：排除WebSocket自动配置
        org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration.class
    }
)
public class HighBatchCenterApplication {
    // 现有代码...
}
```

### 方案5: 强制指定Tomcat版本

在父pom.xml中添加版本管理：

```xml
<properties>
    <!-- 现有配置... -->
    <tomcat.version>9.0.65</tomcat.version> <!-- 与Spring Boot 2.3.12兼容的版本 -->
</properties>

<dependencyManagement>
    <dependencies>
        <!-- 现有依赖管理... -->
        
        <!-- 强制指定Tomcat版本 -->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>${tomcat.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
            <version>${tomcat.version}</version>
        </dependency>
    </dependencies>
</dependencyManagement>
```

## 📋 **实施步骤**

### 第一步：快速修复（推荐方案2）
1. 在配置文件中禁用WebSocket
2. 重启应用验证问题是否解决

### 第二步：彻底解决（推荐方案1）
1. 在pom.xml中排除冲突的WebSocket依赖
2. 清理Maven缓存：`mvn clean`
3. 重新编译：`mvn compile`
4. 重启应用

### 第三步：验证修复
1. 检查应用启动日志
2. 确认没有WebSocket相关错误
3. 验证业务功能正常

## 🚨 **注意事项**

### 1. 业务影响评估
- **如果项目不使用WebSocket功能**：可以安全禁用
- **如果项目使用WebSocket**：需要仔细测试功能

### 2. Dubbo 3.x兼容性
- 确保Dubbo 3.x版本与Spring Boot版本兼容
- 检查Dubbo官方兼容性文档

### 3. 测试建议
- 在开发环境先验证修复方案
- 重点测试消息队列和Dubbo服务调用
- 确认日志输出正常

## 🎯 **根本解决建议**

### 长期方案
1. **版本统一管理**：
   - 建立统一的版本管理策略
   - 定期更新依赖版本

2. **依赖冲突检测**：
   - 使用Maven依赖分析工具
   - 定期检查版本冲突

3. **测试覆盖**：
   - 增加集成测试
   - 验证关键功能稳定性

### 监控建议
- 监控应用启动时间
- 关注内存使用情况
- 检查是否有其他版本冲突警告

## 总结

这个错误是Dubbo 2.x升级到3.x过程中的常见问题，主要由WebSocket API版本冲突引起。推荐先使用**方案2（禁用WebSocket）**快速解决，然后实施**方案1（排除冲突依赖）**进行彻底修复。
